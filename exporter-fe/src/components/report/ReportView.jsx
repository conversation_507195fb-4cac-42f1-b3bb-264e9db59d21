import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useReport } from '../../hooks/useReport';
import { useSelector } from 'react-redux';
import ReportHeader from './ReportHeader';
import Report from './Report';
import PageSettings from '../page/PageSettings';
import DebugPanel from '../debug/DebugPanel';
import { createTextComponent, createImageComponent, createTableComponent } from '../../models/ComponentTypes';
import '../editor/Editor.css';
import './Report.css';

/**
 * Component to view a specific report by ID
 */
const ReportView = () => {
  const { userId, reportSlugAndId } = useParams();
  const navigate = useNavigate();

  // Extract the reportId from the slug-id format
  // The reportId is the part after the last hyphen
  const reportId = reportSlugAndId ? reportSlugAndId.substring(reportSlugAndId.lastIndexOf('-') + 1) : null;

  console.log('ReportView - URL parameters:', { userId, reportSlugAndId });
  console.log('ReportView - Extracted reportId:', reportId);

  // For debugging, also log the full URL
  console.log('ReportView - Full URL:', window.location.href);
  const {
    activeReport,
    activePage,
    updatePage,
    updateComponent,
    selectComponent,
    loadReportById,
    addComponent,
    isLoading,
    error
  } = useReport();

  const currentUser = useSelector(state => state.auth.user);

  // Get the selected component
  const selectedComponent = activePage && activePage.selectedComponentId
    ? activePage.components.find(comp => comp.id === activePage.selectedComponentId)
    : null;

  // Load the report when the component mounts
  useEffect(() => {
    let isMounted = true; // Flag to prevent state updates after unmount

    const loadReport = async () => {
      try {
        console.log('ReportView - Loading report with ID:', reportId);
        console.log('ReportView - URL parameters:', { userId, reportSlugAndId });

        if (!reportId) {
          console.error('ReportView - No reportId found in URL');
          if (isMounted) navigate('/');
          return;
        }

        console.log('ReportView - About to call loadReportById with ID:', reportId);
        const loadedReport = await loadReportById(reportId);

        if (!isMounted) return; // Don't update state if component unmounted

        console.log('ReportView - Report loaded successfully:', loadedReport);
      } catch (error) {
        console.error('ReportView - Error loading report:', error);

        if (!isMounted) return; // Don't navigate if component unmounted

        // Add a small delay before navigating to ensure loading state is reset
        setTimeout(() => {
          if (isMounted) navigate('/');
        }, 100);
      }
    };

    // Only try to load the report if we have a reportId
    if (reportId) {
      console.log('ReportView - Initiating report load for ID:', reportId);
      loadReport();
    } else {
      console.error('ReportView - No reportId available, cannot load report');
      navigate('/');
    }

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [reportId, reportSlugAndId, userId, loadReportById, navigate]);

  // Check if the user has permission to view this report
  useEffect(() => {
    let isMounted = true;

    if (currentUser && userId) {
      console.log('ReportView - Checking user permission:', {
        currentUserId: currentUser.uid,
        urlUserId: userId
      });

      if (userId !== currentUser.uid) {
        console.error('ReportView - Permission denied: URL user ID does not match current user');

        // Add a small delay before navigating to ensure loading state is reset
        setTimeout(() => {
          if (isMounted) navigate('/');
        }, 100);
      } else {
        console.log('ReportView - User has permission to view this report');
      }
    }

    return () => {
      isMounted = false;
    };
  }, [currentUser, userId, navigate]);

  // Handle page updates
  const handlePageUpdate = (updates) => {
    if (activeReport && activePage) {
      updatePage(activeReport.id, activePage.id, updates);
    }
  };

  // Handle adding a text component
  const handleAddTextComponent = () => {
    if (activeReport && activePage) {
      // Create a text component at the center of the visible area
      const textComponent = createTextComponent(
        `text-${Date.now()}`,
        activePage.margins.left + 20, // X position (20mm from left margin)
        activePage.margins.top + 20    // Y position (20mm from top margin)
      );

      addComponent(activeReport.id, activePage.id, textComponent);
    }
  };

  // Handle adding an image component
  const handleAddImageComponent = () => {
    if (activeReport && activePage) {
      // Create an image component at the center of the visible area
      const imageComponent = createImageComponent(
        `image-${Date.now()}`,
        activePage.margins.left + 20, // X position (20mm from left margin)
        activePage.margins.top + 50    // Y position (50mm from top margin)
      );

      addComponent(activeReport.id, activePage.id, imageComponent);
    }
  };

  // Handle adding a table component
  const handleAddTableComponent = () => {
    if (activeReport && activePage) {
      // Create a table component at the center of the visible area
      const tableComponent = createTableComponent(
        `table-${Date.now()}`,
        activePage.margins.left + 20, // X position (20mm from left margin)
        activePage.margins.top + 80    // Y position (80mm from top margin)
      );

      addComponent(activeReport.id, activePage.id, tableComponent);
    }
  };

  // Handle debug panel actions
  const handleDebugSelectComponent = (componentId) => {
    if (activeReport && activePage) {
      selectComponent(activeReport.id, activePage.id, componentId);
    }
  };

  const handleDebugDeselectComponent = () => {
    if (activeReport && activePage) {
      selectComponent(activeReport.id, activePage.id, null);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="app-loading">
        <div className="loading-spinner"></div>
        <p>Loading report...</p>
        <div className="loading-details">
          <p>Report ID: {reportId}</p>
          <button
            onClick={() => navigate('/')}
            className="cancel-button"
          >
            Cancel
          </button>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="error-message">
        <h2>Error</h2>
        <p>{error}</p>
        <p className="error-details">Report ID: {reportId}</p>
        <button
          onClick={() => navigate('/')}
          className="home-button"
        >
          Go Home
        </button>
      </div>
    );
  }

  // Show report not found state
  if (!activeReport) {
    return (
      <div className="not-found-message">
        <h2>Report Not Found</h2>
        <p>The report you are looking for does not exist or you do not have permission to view it.</p>
        <p className="error-details">Report ID: {reportId}</p>
        <button
          onClick={() => navigate('/')}
          className="home-button"
        >
          Go Home
        </button>
      </div>
    );
  }

  return (
    <>
      {/* Debug Panel - Rendered outside app-layout to avoid overflow issues */}
      <DebugPanel
        activeReport={activeReport}
        activePage={activePage}
        selectedComponent={selectedComponent}
        onSelectComponent={handleDebugSelectComponent}
        onDeselectComponent={handleDebugDeselectComponent}
      />

      <div className="app-layout">
        <div className="editor-container">
        <ReportHeader />

        <div className="editor-main">
          <div className="editor-toolbar">
            <div className="tool-group">
              <button
                className="tool-button"
                onClick={handleAddTextComponent}
                disabled={!activePage}
              >
                Add Text
              </button>
              <button
                className="tool-button"
                onClick={handleAddImageComponent}
                disabled={!activePage}
              >
                Add Image
              </button>
              <button
                className="tool-button"
                onClick={handleAddTableComponent}
                disabled={!activePage}
              >
                Add Table
              </button>
            </div>
          </div>

          <div className="editor-content">
            <Report />
          </div>

          <div className="editor-sidebar">
            {activePage ? (
              <PageSettings
                page={activePage}
                onUpdate={handlePageUpdate}
              />
            ) : (
              <div className="no-page-selected">
                <p>No page selected</p>
              </div>
            )}
          </div>
        </div>
        </div>
      </div>
    </>
  );
};

export default ReportView;
