import React from 'react';
import PropTypes from 'prop-types';
import './DebugPanel.css';

/**
 * Debug panel component to show current state
 */
const DebugPanel = ({ 
  activeReport, 
  activePage, 
  selectedComponent,
  onSelectComponent,
  onDeselectComponent 
}) => {
  return (
    <div className="debug-panel">
      <div className="debug-header">🐛 DEBUG PANEL</div>
      
      <div className="debug-section">
        <div className="debug-label">Report State:</div>
        <div>Active Report: {activeReport ? '✅ Yes' : '❌ No'}</div>
        <div>Active Page: {activePage ? '✅ Yes' : '❌ No'}</div>
      </div>

      {activePage && (
        <div className="debug-section">
          <div className="debug-label">Page Info:</div>
          <div>Page ID: {activePage.id}</div>
          <div>Components: {activePage.components.length}</div>
          <div>Selected ID: {activePage.selectedComponentId || 'None'}</div>
        </div>
      )}

      {selectedComponent && (
        <div className="debug-section">
          <div className="debug-label">Selected Component:</div>
          <div>Type: {selectedComponent.type}</div>
          <div>ID: {selectedComponent.id}</div>
          <div>Width: {selectedComponent.width}mm</div>
          <div>Height: {selectedComponent.height}mm</div>
          <div>X: {selectedComponent.x}mm</div>
          <div>Y: {selectedComponent.y}mm</div>
        </div>
      )}

      <div className="debug-section">
        <div className="debug-label">Panel Status:</div>
        <div>Should Show: {selectedComponent ? 'Element Settings' : 'Page Settings'}</div>
      </div>

      {activePage && activePage.components.length > 0 && (
        <div className="debug-section">
          <div className="debug-label">Test Actions:</div>
          <button 
            className="debug-button"
            onClick={() => onSelectComponent(activePage.components[0].id)}
          >
            Select First Component
          </button>
          <button 
            className="debug-button"
            onClick={onDeselectComponent}
          >
            Deselect All
          </button>
        </div>
      )}

      <div className="debug-section">
        <button 
          className="debug-button test-button"
          onClick={() => alert('Debug panel is working!')}
        >
          Test Alert
        </button>
      </div>
    </div>
  );
};

DebugPanel.propTypes = {
  activeReport: PropTypes.object,
  activePage: PropTypes.object,
  selectedComponent: PropTypes.object,
  onSelectComponent: PropTypes.func,
  onDeselectComponent: PropTypes.func
};

export default DebugPanel;
